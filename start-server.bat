@echo off
echo ========================================
echo       موقع الذِّكر الحكيم
echo    منصة إسلامية شاملة
echo ========================================
echo.

echo جاري تشغيل الخادم المحلي...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo تم العثور على Python، جاري تشغيل الخادم...
    echo الموقع متاح على: http://localhost:8000
    echo.
    echo لإيقاف الخادم اضغط Ctrl+C
    echo ========================================
    python -m http.server 8000
) else (
    REM التحقق من وجود Node.js
    node --version >nul 2>&1
    if %errorlevel% == 0 (
        echo تم العثور على Node.js، جاري تشغيل الخادم...
        echo الموقع متاح على: http://localhost:3000
        echo.
        echo لإيقاف الخادم اضغط Ctrl+C
        echo ========================================
        npx serve . -p 3000
    ) else (
        echo لم يتم العثور على Python أو Node.js
        echo سيتم فتح الملف مباشرة في المتصفح...
        echo ========================================
        start index.html
    )
)

pause
