# ===== تحسين الأداء والأمان لموقع الذِّكر الحكيم =====

# تفعيل ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# تفعيل التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # JSON و XML
    ExpiresByType application/json "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"
</IfModule>

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تشغيل السكريبت الضار
    Header set X-XSS-Protection "1; mode=block"
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # سياسة الأمان للمحتوى
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self';"
    
    # إزالة معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# إعادة توجيه HTTPS (إذا كان متاحاً)
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # إعادة توجيه إلى HTTPS
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # إزالة .html من الروابط
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^([^\.]+)$ $1.html [NC,L]
    
    # إعادة توجيه الصفحة الرئيسية
    RewriteRule ^home$ index.html [NC,L]
    RewriteRule ^الرئيسية$ index.html [NC,L]
    
    # إعادة توجيه صفحة الخدمات
    RewriteRule ^خدمات$ services.html [NC,L]
    RewriteRule ^الخدمات$ services.html [NC,L]
    
    # إعادة توجيه صفحة حولنا
    RewriteRule ^حولنا$ about.html [NC,L]
    RewriteRule ^عنا$ about.html [NC,L]
</IfModule>

# منع الوصول للملفات الحساسة
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "README.md">
    Order allow,deny
    Deny from all
</Files>

# تحسين الترميز
AddDefaultCharset UTF-8

# تفعيل Keep-Alive
<IfModule mod_headers.c>
    Header set Connection keep-alive
</IfModule>

# صفحات الأخطاء المخصصة (اختياري)
# ErrorDocument 404 /404.html
# ErrorDocument 500 /500.html

# تحسين الأداء للملفات الثابتة
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "max-age=31536000, public"
    </FilesMatch>
    
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "max-age=3600, public"
    </FilesMatch>
</IfModule>
