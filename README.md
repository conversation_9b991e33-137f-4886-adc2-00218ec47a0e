# الذِّكر الحكيم - موقع إسلامي حديث واحترافي

## 🌙 نظرة عامة

موقع "الذِّكر الحكيم" هو منصة إسلامية شاملة تهدف إلى تقريب المسلمين من كتاب الله عز وجل وسنة نبيه الكريم صلى الله عليه وسلم. يتميز الموقع بتصميم حديث واحترافي يراعي أفضل معايير التصميم (UI/UX) مع الحفاظ على الطابع الإسلامي الأصيل.

## ✨ الميزات الرئيسية

### 🎨 التصميم
- **وضع ليلي افتراضي** مريح للعينين
- **تأثيرات النوافذ الزجاجية (Glassmorphism)** عصرية وأنيقة
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **خطوط عربية جميلة** (Cairo & Amiri)
- **ألوان متناسقة** مع التباين المناسب

### 📱 التقنيات المستخدمة
- **HTML5** - هيكل الموقع
- **Bootstrap 5.3.2 RTL** - إطار العمل للتصميم المتجاوب
- **CSS3** - التنسيق والتأثيرات المخصصة
- **JavaScript (Vanilla)** - التفاعلات والأنيميشن
- **Font Awesome** - الأيقونات والرموز
- **Google Fonts** - الخطوط العربية (Cairo & Amiri)

### 🏗️ هيكل الموقع

```
alzekr_alhakem/
├── index.html          # الصفحة الرئيسية
├── services.html       # صفحة الخدمات
├── about.html         # صفحة حولنا
├── css/
│   └── style.css      # ملف التنسيق الرئيسي
├── js/
│   └── script.js      # ملف JavaScript
├── assets/
│   ├── images/        # الصور والشعارات
│   ├── icons/         # الأيقونات المخصصة
│   └── fonts/         # الخطوط المخصصة
└── README.md          # ملف التوثيق
```

## 📄 الصفحات

### 🏠 الصفحة الرئيسية (index.html)
- رسالة ترحيبية دينية جميلة
- إحصائيات القرآن الكريم في نوافذ زجاجية:
  - عدد الصفحات (604)
  - عدد الأجزاء (30)
  - عدد السور (114)
  - عدد الآيات (6236)
- معاينة سريعة للخدمات
- تصميم تفاعلي مع تأثيرات Hover

### 🛠️ صفحة الخدمات (services.html)
- **قراءة القرآن الكريم** - بخط واضح وجميل
- **أحاديث نبوية شريفة** - من المصادر المعتمدة
- **المسبحة الإلكترونية** - عداد ذكي تفاعلي
- **التقويم الهجري** - مع المناسبات الإسلامية
- **الأذكار اليومية** - أذكار الصباح والمساء
- **أسماء الله الحسنى** - مع شرح المعاني

### ℹ️ صفحة حولنا (about.html)
- رسالة وأهداف الموقع
- القيم التي نؤمن بها
- اقتباسات من القرآن والسنة
- معلومات عن المطور

## 🎯 الميزات التقنية

### 🌐 تصميم متجاوب
- يعمل بسلاسة على الهواتف الذكية
- متوافق مع الأجهزة اللوحية
- محسن لأجهزة الكمبيوتر

### ⚡ الأداء
- تحميل سريع وخفيف
- تأثيرات CSS محسنة
- JavaScript مُحسن للأداء
- تحميل تدريجي للمحتوى

### 🎨 التأثيرات البصرية
- أنيميشن سلس للعناصر مع Bootstrap
- تأثيرات Hover تفاعلية محسنة
- أنيميشن للأرقام والإحصائيات
- تأثيرات الموجة عند النقر (Ripple Effects)
- خلفية متحركة تفاعلية
- نظام Grid متجاوب مع Bootstrap 5.3.2
- مكونات Bootstrap المخصصة (Cards, Navbar, Buttons)

### 🅱️ ميزات Bootstrap المستخدمة
- **Bootstrap 5.3.2 RTL** - دعم كامل للغة العربية
- **نظام Grid المتقدم** - تخطيط متجاوب مثالي
- **مكونات تفاعلية** - Navbar, Cards, Buttons, Badges
- **فئات المساعدة** - Spacing, Colors, Typography
- **تصميم متجاوب** - Breakpoints محسنة للأجهزة المختلفة

## 🚀 كيفية التشغيل

1. **تحميل الملفات**
   ```bash
   git clone [repository-url]
   cd alzekr_alhakem
   ```

2. **فتح الموقع**
   - افتح ملف `index.html` في المتصفح
   - أو استخدم خادم محلي:
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # باستخدام Node.js
   npx serve .
   ```

3. **التصفح**
   - انتقل إلى `http://localhost:8000`
   - استمتع بتصفح الموقع!

## 🎨 دليل الألوان

```css
/* الألوان الرئيسية */
--primary-color: #1a1a2e      /* أزرق داكن */
--secondary-color: #16213e    /* أزرق متوسط */
--accent-color: #0f3460       /* أزرق فاتح */
--gold-color: #d4af37         /* ذهبي */
--light-gold: #f4e4a6         /* ذهبي فاتح */
--text-primary: #ffffff       /* أبيض */
--text-secondary: #b8b8b8     /* رمادي فاتح */
```

## 📱 التوافق

### المتصفحات المدعومة
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### الأجهزة المدعومة
- ✅ الهواتف الذكية (320px+)
- ✅ الأجهزة اللوحية (768px+)
- ✅ أجهزة الكمبيوتر (1024px+)
- ✅ الشاشات الكبيرة (1440px+)

## 🔧 التخصيص

### تغيير الألوان
عدّل المتغيرات في ملف `css/style.css`:
```css
:root {
    --primary-color: #your-color;
    --gold-color: #your-gold;
    /* ... باقي الألوان */
}
```

### إضافة خدمات جديدة
1. أضف بطاقة خدمة جديدة في `services.html`
2. أضف التنسيقات المطلوبة في `css/style.css`
3. أضف التفاعلات في `js/script.js`

## 👨‍💻 المطور

**Osama Developer**
- مطور ويب متخصص في المواقع الإسلامية
- "نُورُكَ في القرآن، فلتبصر به قلبك"

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

## 🤝 المساهمة

نرحب بالمساهمات لتحسين الموقع:
1. Fork المشروع
2. أنشئ فرع جديد للميزة
3. اعمل التغييرات المطلوبة
4. أرسل Pull Request

## 📞 التواصل

لأي استفسارات أو اقتراحات، يرجى التواصل معنا.

---

**"وَلَقَدْ يَسَّرْنَا الْقُرْآنَ لِلذِّكْرِ فَهَلْ مِن مُّدَّكِرٍ"**

*جعل الله هذا العمل في ميزان حسناتنا وحسناتكم* 🤲
