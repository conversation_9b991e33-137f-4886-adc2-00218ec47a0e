/* ===== إعدادات عامة مع Bootstrap ===== */
:root {
    /* ألوان الوضع المظلم */
    --primary-color: #1a1a2e;
    --secondary-color: #16213e;
    --accent-color: #0f3460;
    --gold-color: #d4af37;
    --light-gold: #f4e4a6;
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --gradient-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    --gradient-gold: linear-gradient(135deg, #d4af37 0%, #f4e4a6 100%);
}

/* تخصيص Bootstrap للغة العربية */
body {
    font-family: 'Cairo', '<PERSON><PERSON>', Arial, sans-serif !important;
    background: var(--gradient-primary) !important;
    line-height: 1.6;
    min-height: 100vh;
    direction: rtl;
    overflow-x: hidden;
}

/* تخصيص ألوان Bootstrap */
.text-warning {
    color: var(--gold-color) !important;
}

.text-warning-emphasis {
    color: var(--light-gold) !important;
}

.btn-warning {
    background: var(--gradient-gold) !important;
    border-color: var(--gold-color) !important;
    color: var(--primary-color) !important;
    font-weight: 600;
}

.btn-warning:hover {
    background: var(--light-gold) !important;
    border-color: var(--light-gold) !important;
    color: var(--primary-color) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

/* ===== تأثيرات الخلفية ===== */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(15, 52, 96, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(22, 33, 62, 0.1) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

/* ===== تخصيص شريط التنقل ===== */
.glass-navbar {
    background: rgba(26, 26, 46, 0.9) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: 0 2px 20px var(--shadow-color);
}

.navbar-brand {
    font-size: 1.5rem !important;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0 5px;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--gold-color) !important;
    background: var(--glass-bg);
    transform: translateY(-2px);
}

.navbar-toggler {
    border: none !important;
    padding: 4px 8px;
}

.navbar-toggler:focus {
    box-shadow: none !important;
}

/* ===== تأثيرات النوافذ الزجاجية مع Bootstrap ===== */
.glass-card {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border) !important;
    border-radius: 20px !important;
    box-shadow:
        0 8px 32px var(--shadow-color),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    z-index: 1;
}

.glass-card:hover {
    transform: translateY(-5px);
    box-shadow:
        0 15px 40px var(--shadow-color),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(212, 175, 55, 0.3) !important;
}

/* تأثيرات خاصة للبطاقات */
.stat-card:hover {
    transform: translateY(-10px) scale(1.02);
}

.service-preview-card:hover {
    transform: translateY(-8px);
}

/* النمط الإسلامي */
.islamic-pattern {
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle, rgba(212, 175, 55, 0.05) 1px, transparent 1px);
    background-size: 30px 30px;
    animation: patternRotate 30s linear infinite;
    z-index: 0;
}

@keyframes patternRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== تأثيرات إضافية ===== */
/* تأثير توهج الشعار */
.navbar-brand i {
    animation: logoGlow 3s ease-in-out infinite;
}

@keyframes logoGlow {
    0%, 100% { text-shadow: 0 0 10px rgba(212, 175, 55, 0.5); }
    50% { text-shadow: 0 0 20px rgba(212, 175, 55, 0.8); }
}

/* تأثيرات الأيقونات */
.stat-icon i {
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* تأثيرات النصوص */
.display-4, .display-5 {
    text-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
}

/* تحسين المسافات */
.main-content {
    padding-top: 80px;
}

/* تأثيرات الأزرار */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

/* تخصيص الخلفيات */
.bg-dark-subtle {
    background: rgba(0, 0, 0, 0.2) !important;
}

/* تحسين النصوص العربية */
.lead {
    font-family: 'Amiri', serif !important;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif !important;
}

/* تأثيرات التمرير */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* تحسين الفوتر */
.footer a:hover {
    color: var(--gold-color) !important;
    text-decoration: none !important;
}

/* تأثيرات الأنيميشن للأرقام */
.stat-number {
    transition: all 0.3s ease;
}

.stat-card:hover .stat-number {
    transform: scale(1.1);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

/* ===== تنسيقات متجاوبة مع Bootstrap ===== */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem !important;
    }

    .display-5 {
        font-size: 1.5rem !important;
    }

    .main-content {
        padding-top: 70px;
    }

    .glass-card {
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2.5rem !important;
    }
}

@media (max-width: 576px) {
    .display-4 {
        font-size: 1.5rem !important;
    }

    .display-5 {
        font-size: 1.3rem !important;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem !important;
        font-size: 1rem !important;
    }

    .glass-card .card-body {
        padding: 2rem 1rem !important;
    }
}

/* ===== تأثيرات خاصة للموبايل ===== */
@media (hover: none) {
    .card:hover {
        transform: none;
    }

    .stat-card:hover {
        transform: none;
    }

    .btn:hover {
        transform: none;
    }
}

/* ===== تأثيرات إضافية للتفاعل ===== */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* تحسين الخطوط العربية */
.arabic-text {
    font-family: 'Amiri', serif !important;
    line-height: 1.8;
}

/* تأثيرات خاصة للاقتباسات */
.quote-text {
    position: relative;
    font-style: italic;
}

.quote-icon {
    opacity: 0.3;
    transition: all 0.3s ease;
}

.glass-card:hover .quote-icon {
    opacity: 0.6;
    transform: scale(1.1);
}

/* ===== تأثيرات خاصة للنصوص ===== */
@keyframes starPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.navbar-brand i {
    animation: starPulse 2s ease-in-out infinite;
}

/* تحسين عرض المحتوى */
.card-body {
    position: relative;
    z-index: 2;
}

/* تأثيرات خاصة للإحصائيات */
.stat-number {
    font-weight: 700 !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* تحسين الألوان */
.text-light-emphasis {
    color: rgba(255, 255, 255, 0.75) !important;
}

/* تأثيرات الحدود */
.border-secondary {
    border-color: var(--glass-border) !important;
}
/* ===== نهاية ملف CSS المحسن لـ Bootstrap ===== */



.section-title i {
    font-size: 2.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.stat-card {
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-10px) scale(1.02);
}

.stat-icon {
    font-size: 3rem;
    color: var(--gold-color);
    margin-bottom: 1rem;
    animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.stat-label {
    font-size: 1.3rem;
    color: var(--gold-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.stat-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* ===== الخدمات السريعة ===== */
.quick-services {
    padding: 4rem 0;
    background: rgba(0, 0, 0, 0.1);
}

.services-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.service-preview-card {
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.service-preview-card:hover {
    transform: translateY(-5px);
}

.service-preview-card i {
    font-size: 3rem;
    color: var(--gold-color);
    margin-bottom: 1rem;
}

.service-preview-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.service-preview-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.cta-button {
    text-align: center;
    margin-top: 3rem;
}

.btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background: var(--gradient-gold);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
}

/* ===== الفوتر ===== */
.footer {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--glass-border);
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-section h3 {
    color: var(--gold-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gold-color);
    margin-bottom: 1rem;
}

.footer-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-links a:hover {
    color: var(--gold-color);
}

.developer-info p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.inspirational-quote {
    font-style: italic;
    color: var(--light-gold) !important;
    font-family: 'Amiri', serif;
}

.footer-bottom {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--glass-border);
    color: var(--text-secondary);
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        right: -100%;
        top: 80px;
        flex-direction: column;
        background: rgba(26, 26, 46, 0.95);
        backdrop-filter: blur(20px);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
        border-top: 1px solid var(--glass-border);
    }

    .nav-menu.active {
        right: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .services-preview {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 0 1rem;
    }
}

/* ===== صفحة الخدمات ===== */
.page-header {
    padding: 2rem 0;
    text-align: center;
}

.header-content {
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.page-title {
    font-size: 2.5rem;
    color: var(--gold-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.page-subtitle {
    font-family: 'Amiri', serif;
    font-size: 1.2rem;
    color: var(--light-gold);
    font-style: italic;
}

.services-section {
    padding: 3rem 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-card {
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.service-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::after {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(212, 175, 55, 0.5);
}

.service-icon {
    font-size: 4rem;
    color: var(--gold-color);
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.service-title {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.service-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

.feature-tag {
    background: rgba(212, 175, 55, 0.2);
    color: var(--gold-color);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.service-btn {
    background: var(--gradient-gold);
    color: var(--primary-color);
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.service-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

/* ===== الميزات الإضافية ===== */
.additional-features {
    padding: 3rem 0;
    background: rgba(0, 0, 0, 0.1);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-item i {
    font-size: 3rem;
    color: var(--gold-color);
    margin-bottom: 1rem;
}

.feature-item h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.feature-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== صفحة حولنا ===== */
.about-intro {
    padding: 3rem 0;
}

.intro-content {
    padding: 3rem;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 3rem;
    align-items: center;
}

.intro-text h2 {
    color: var(--gold-color);
    margin-bottom: 1.5rem;
    font-size: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.intro-text p {
    color: var(--text-secondary);
    line-height: 1.8;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.intro-icon {
    font-size: 8rem;
    color: var(--gold-color);
    opacity: 0.3;
    animation: iconPulse 4s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.1); opacity: 0.5; }
}

.goals-section {
    padding: 3rem 0;
}

.goals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.goal-card {
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.goal-card:hover {
    transform: translateY(-5px);
}

.goal-icon {
    font-size: 3rem;
    color: var(--gold-color);
    margin-bottom: 1rem;
}

.goal-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.goal-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.values-section {
    padding: 3rem 0;
    background: rgba(0, 0, 0, 0.1);
}

.values-content {
    display: grid;
    gap: 2rem;
    margin-top: 2rem;
}

.value-item {
    padding: 2rem;
    transition: all 0.3s ease;
}

.value-item:hover {
    transform: translateX(10px);
}

.value-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.value-header i {
    font-size: 2rem;
    color: var(--gold-color);
}

.value-header h3 {
    color: var(--text-primary);
    font-size: 1.3rem;
}

.value-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.quotes-section {
    padding: 3rem 0;
}

.quotes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.quote-card {
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.quote-card:hover {
    transform: translateY(-5px);
}

.quote-text {
    position: relative;
    margin-bottom: 1.5rem;
}

.quote-icon {
    font-size: 2rem;
    color: var(--gold-color);
    opacity: 0.3;
    position: absolute;
    top: -10px;
    right: -10px;
}

.quote-text p {
    font-family: 'Amiri', serif;
    font-size: 1.2rem;
    color: var(--text-primary);
    line-height: 1.8;
    font-style: italic;
}

.quote-source {
    color: var(--gold-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.developer-section {
    padding: 3rem 0;
}

.developer-card {
    padding: 3rem;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 3rem;
    align-items: center;
}

.developer-content h2 {
    color: var(--gold-color);
    margin-bottom: 2rem;
    font-size: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.developer-info h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.developer-info p {
    color: var(--text-secondary);
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.developer-quote {
    background: rgba(212, 175, 55, 0.1);
    padding: 1.5rem;
    border-radius: 15px;
    border-right: 4px solid var(--gold-color);
}

.developer-quote p {
    font-family: 'Amiri', serif;
    color: var(--light-gold) !important;
    font-style: italic;
    margin: 0;
}

.developer-avatar {
    font-size: 8rem;
    color: var(--gold-color);
    opacity: 0.3;
}

@media (max-width: 768px) {
    .intro-content,
    .developer-card {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .intro-icon,
    .developer-avatar {
        font-size: 5rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .welcome-card {
        padding: 2rem 1rem;
    }

    .service-card {
        padding: 1.5rem;
    }

    .page-title {
        font-size: 1.8rem;
    }
}
