// ===== تفعيل قائمة التنقل للموبايل =====
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // تفعيل/إلغاء تفعيل قائمة الهامبرغر
    if (hamburger) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // إغلاق القائمة عند النقر على رابط
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // إغلاق القائمة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        }
    });
});

// ===== تأثيرات التمرير =====
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    
    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(26, 26, 46, 0.95)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.3)';
    } else {
        navbar.style.background = 'rgba(26, 26, 46, 0.9)';
        navbar.style.boxShadow = 'none';
    }
});

// ===== تأثيرات الأنيميشن عند الظهور =====
function animateOnScroll() {
    const elements = document.querySelectorAll('.glass-card, .stat-card, .service-card');
    
    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        
        if (elementTop < window.innerHeight - elementVisible) {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }
    });
}

// تطبيق الأنيميشن عند تحميل الصفحة وعند التمرير
window.addEventListener('scroll', animateOnScroll);
window.addEventListener('load', animateOnScroll);

// ===== تأثيرات الإحصائيات المتحركة =====
function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(stat => {
        const target = parseInt(stat.textContent);
        const increment = target / 100;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            stat.textContent = Math.floor(current);
        }, 20);
    });
}

// تشغيل أنيميشن الأرقام عند ظهور القسم
function checkStatsVisibility() {
    const statsSection = document.querySelector('.stats-section');
    if (statsSection) {
        const sectionTop = statsSection.getBoundingClientRect().top;
        const sectionVisible = 200;
        
        if (sectionTop < window.innerHeight - sectionVisible && !statsSection.classList.contains('animated')) {
            statsSection.classList.add('animated');
            animateNumbers();
        }
    }
}

window.addEventListener('scroll', checkStatsVisibility);
window.addEventListener('load', checkStatsVisibility);

// ===== تأثيرات بطاقات الخدمات =====
document.addEventListener('DOMContentLoaded', function() {
    const serviceCards = document.querySelectorAll('.service-card');
    
    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // إضافة تأثير صوتي خفيف (اختياري)
            this.style.transform = 'translateY(-8px) scale(1.02)';
            
            // تأثير الإضاءة
            const icon = this.querySelector('.service-icon i');
            if (icon) {
                icon.style.textShadow = '0 0 20px rgba(212, 175, 55, 0.8)';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            
            const icon = this.querySelector('.service-icon i');
            if (icon) {
                icon.style.textShadow = '';
            }
        });
    });
});

// ===== تأثيرات الأزرار =====
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.service-btn, .btn-primary');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // تأثير الموجة عند النقر
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// ===== تأثيرات الاقتباسات =====
document.addEventListener('DOMContentLoaded', function() {
    const quoteCards = document.querySelectorAll('.quote-card');
    
    quoteCards.forEach((card, index) => {
        // تأثير ظهور متدرج
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
        
        // تأثير التفاعل
        card.addEventListener('mouseenter', function() {
            const quoteIcon = this.querySelector('.quote-icon');
            if (quoteIcon) {
                quoteIcon.style.transform = 'scale(1.2) rotate(10deg)';
                quoteIcon.style.opacity = '0.6';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            const quoteIcon = this.querySelector('.quote-icon');
            if (quoteIcon) {
                quoteIcon.style.transform = '';
                quoteIcon.style.opacity = '0.3';
            }
        });
    });
});

// ===== تأثيرات التمرير السلس =====
document.addEventListener('DOMContentLoaded', function() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// ===== تأثيرات الخلفية التفاعلية =====
document.addEventListener('mousemove', function(e) {
    const mouseX = e.clientX / window.innerWidth;
    const mouseY = e.clientY / window.innerHeight;
    
    // تحريك النمط الإسلامي بناءً على موضع الماوس
    const islamicPattern = document.querySelector('.islamic-pattern');
    if (islamicPattern) {
        const moveX = (mouseX - 0.5) * 20;
        const moveY = (mouseY - 0.5) * 20;
        islamicPattern.style.transform = `translate(${moveX}px, ${moveY}px) rotate(${Date.now() * 0.001}rad)`;
    }
});

// ===== تحسين الأداء =====
// تأخير تحميل الصور (Lazy Loading)
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
});

// ===== إضافة تأثيرات CSS للأنيميشن =====
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .glass-card, .stat-card, .service-card {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }
    
    .quote-card {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.5s ease;
    }
`;
document.head.appendChild(style);

// ===== رسائل ترحيبية =====
console.log('🌙 مرحباً بك في موقع الذِّكر الحكيم');
console.log('📖 "وَلَقَدْ يَسَّرْنَا الْقُرْآنَ لِلذِّكْرِ فَهَلْ مِن مُّدَّكِرٍ"');
console.log('💻 تم تطوير الموقع بواسطة: Osama Developer');
