// ===== تحسينات Bootstrap والتفاعلات =====
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips في Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // إغلاق navbar عند النقر على رابط (للموبايل)
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (navbarCollapse.classList.contains('show')) {
                const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                bsCollapse.hide();
            }
        });
    });
});

// ===== تأثيرات التمرير المحسنة =====
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.glass-navbar');

    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(26, 26, 46, 0.98)';
        navbar.style.boxShadow = '0 4px 30px rgba(0, 0, 0, 0.4)';
        navbar.style.backdropFilter = 'blur(25px)';
    } else {
        navbar.style.background = 'rgba(26, 26, 46, 0.9)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.3)';
        navbar.style.backdropFilter = 'blur(20px)';
    }
});

// ===== تأثيرات الأنيميشن المحسنة مع Bootstrap =====
function animateOnScroll() {
    const elements = document.querySelectorAll('.card, .glass-card');

    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < window.innerHeight - elementVisible) {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
            element.classList.add('animate__fadeInUp');
        }
    });
}

// تطبيق الأنيميشن عند تحميل الصفحة وعند التمرير
window.addEventListener('scroll', animateOnScroll);
window.addEventListener('load', animateOnScroll);

// إضافة تأثيرات CSS للأنيميشن
const style = document.createElement('style');
style.textContent = `
    .card, .glass-card {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }

    .animate__fadeInUp {
        opacity: 1 !important;
        transform: translateY(0) !important;
    }
`;
document.head.appendChild(style);

// ===== تأثيرات الإحصائيات المتحركة المحسنة =====
function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');

    statNumbers.forEach(stat => {
        const target = parseInt(stat.textContent);
        const increment = target / 80; // تحسين السرعة
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            stat.textContent = Math.floor(current);
        }, 25);
    });
}

// تشغيل أنيميشن الأرقام عند ظهور القسم
function checkStatsVisibility() {
    const statsSection = document.querySelector('.stats-section');
    if (statsSection) {
        const sectionTop = statsSection.getBoundingClientRect().top;
        const sectionVisible = 200;

        if (sectionTop < window.innerHeight - sectionVisible && !statsSection.classList.contains('animated')) {
            statsSection.classList.add('animated');
            setTimeout(animateNumbers, 300); // تأخير بسيط للتأثير
        }
    }
}

window.addEventListener('scroll', checkStatsVisibility);
window.addEventListener('load', checkStatsVisibility);

// ===== تأثيرات بطاقات الخدمات المحسنة =====
document.addEventListener('DOMContentLoaded', function() {
    const serviceCards = document.querySelectorAll('.service-card, .card');

    serviceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            // تأثير الرفع والتكبير
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 15px 40px rgba(0, 0, 0, 0.4)';

            // تأثير الإضاءة على الأيقونات
            const icons = this.querySelectorAll('i.fa-3x, i.fa-4x');
            icons.forEach(icon => {
                icon.style.textShadow = '0 0 20px rgba(212, 175, 55, 0.8)';
                icon.style.transform = 'scale(1.1)';
            });
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';

            const icons = this.querySelectorAll('i.fa-3x, i.fa-4x');
            icons.forEach(icon => {
                icon.style.textShadow = '';
                icon.style.transform = '';
            });
        });
    });
});

// ===== تأثيرات الأزرار المحسنة =====
document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.btn, .service-btn');

    buttons.forEach(button => {
        // تأثير الموجة عند النقر
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.3)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple-animation 0.6s linear';
            ripple.style.pointerEvents = 'none';

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.remove();
                }
            }, 600);
        });

        // تأثيرات Hover محسنة
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 8px 25px rgba(212, 175, 55, 0.4)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
});

// ===== تأثيرات التمرير السلس =====
document.addEventListener('DOMContentLoaded', function() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// ===== تأثيرات الخلفية التفاعلية =====
document.addEventListener('mousemove', function(e) {
    const mouseX = e.clientX / window.innerWidth;
    const mouseY = e.clientY / window.innerHeight;

    // تحريك النمط الإسلامي بناءً على موضع الماوس
    const islamicPattern = document.querySelector('.islamic-pattern');
    if (islamicPattern) {
        const moveX = (mouseX - 0.5) * 20;
        const moveY = (mouseY - 0.5) * 20;
        islamicPattern.style.transform = `translate(${moveX}px, ${moveY}px) rotate(${Date.now() * 0.001}rad)`;
    }
});

// ===== إضافة تأثيرات CSS للأنيميشن =====
const additionalStyle = document.createElement('style');
additionalStyle.textContent = `
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .btn {
        transition: all 0.3s ease !important;
    }

    .glass-card {
        transition: all 0.3s ease !important;
    }

    .navbar-brand i {
        animation: logoGlow 3s ease-in-out infinite;
    }

    @keyframes logoGlow {
        0%, 100% { text-shadow: 0 0 10px rgba(212, 175, 55, 0.5); }
        50% { text-shadow: 0 0 20px rgba(212, 175, 55, 0.8); }
    }
`;
document.head.appendChild(additionalStyle);

// ===== رسائل ترحيبية =====
console.log('🌙 مرحباً بك في موقع الذِّكر الحكيم');
console.log('📖 "وَلَقَدْ يَسَّرْنَا الْقُرْآنَ لِلذِّكْرِ فَهَلْ مِن مُّدَّكِرٍ"');
console.log('💻 تم تطوير الموقع بواسطة: Osama Developer');
console.log('🚀 تم تحديث الموقع باستخدام Bootstrap 5.3.2');

// ===== تأثيرات التمرير السلس =====
document.addEventListener('DOMContentLoaded', function() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// ===== تأثيرات الخلفية التفاعلية =====
document.addEventListener('mousemove', function(e) {
    const mouseX = e.clientX / window.innerWidth;
    const mouseY = e.clientY / window.innerHeight;
    
    // تحريك النمط الإسلامي بناءً على موضع الماوس
    const islamicPattern = document.querySelector('.islamic-pattern');
    if (islamicPattern) {
        const moveX = (mouseX - 0.5) * 20;
        const moveY = (mouseY - 0.5) * 20;
        islamicPattern.style.transform = `translate(${moveX}px, ${moveY}px) rotate(${Date.now() * 0.001}rad)`;
    }
});


